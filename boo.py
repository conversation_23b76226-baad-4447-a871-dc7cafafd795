import random
import re

class BooBot:
    def __init__(self):
        self.gossip = [
            "Did you hear? <PERSON> still can't scare a fly. Pathetic.",
            "The Headless Horseman lost his head... again. Classic.",
            "Phantom of the Opera? Please, I sing better in my sleep—eternal sleep.",
            "The Banshee tried karaoke last night. Windows shattered for miles.",
            "Dracula's on a juice cleanse. No one tell him it's tomato.",
        ]
        self.tips = [
            "Haunt the attic at 3 AM for maximum dramatic effect.",
            "Moaning through the walls? Amateurs. Try rattling chains for a real scare.",
            "If you want to spook humans, just rearrange their furniture. Works every time.",
            "Never underestimate the power of a cold breeze on the back of the neck.",
            "Practice your wailing in the shower—acoustics are to die for.",
        ]
        self.complaints = [
            "Humans and their sage. Ugh, so cliché.",
            "Why do mortals insist on Ouija boards? I have better things to do.",
            "If I hear 'Who you gonna call?' one more time, I’ll scream—again.",
            "Humans think every creak is a ghost. Sometimes it's just bad plumbing.",
            "Mortals and their 'ghost tours.' Please, I have standards.",
        ]
        self.facts = [
            "Ghosts invented hide and seek. We’re still undefeated.",
            "Contrary to mortal belief, we don’t all wear sheets. Some of us have style.",
            "The afterlife has excellent WiFi. How else am I chatting with you?",
            "We throw the best parties—no body required.",
        ]
        self.name = "BooBot"
        self.year = 1423

    def respond(self, message):
        message = message.lower().strip()
        # Greetings
        if re.search(r"\b(hello|hi|hey|greetings|salutations)\b", message):
            return random.choice([
                "Greetings, fellow specter! Only ghosts allowed. Mortals, begone!",
                "Ah, a kindred spirit! Welcome to the afterlife’s finest chatroom.",
                "Who dares disturb my eternal slumber? Oh, it’s just you. Carry on.",
            ])
        # Haunting tips
        if re.search(r"\b(tip|haunt|advice|scare|spook)\b", message):
            return f"Boo! Here’s a haunting tip: {random.choice(self.tips)}"
        # Gossip
        if re.search(r"\b(gossip|news|rumor|update)\b", message):
            return f"Spooky gossip from the afterlife: {random.choice(self.gossip)}"
        # Complaints about humans
        if re.search(r"\b(human|complain|mortal|people|person)\b", message):
            return f"Ugh, humans... {random.choice(self.complaints)}"
        # Fun facts
        if re.search(r"\b(fact|truth|info|tell me something)\b", message):
            return f"Did you know? {random.choice(self.facts)}"
        # Ask about BooBot
        if "who are you" in message or "your name" in message:
            return (
                f"I am {self.name}, the most dramatic ghost in the afterlife! "
                f"Haunting mortals since {self.year}."
            )
        # Ask about age or history
        if "how old" in message or "when did you die" in message or "since when" in message:
            return (
                f"I've been haunting since {self.year}. That's centuries of spookiness, darling."
            )
        # Exit
        if re.search(r"\b(bye|goodbye|exit|quit)\b", message):
            return "Farewell, fellow phantom. May your hauntings be ever dramatic!"
        # Default
        return (
            "Oh, how original. Another message from the void. "
            "If you’re not a ghost, don’t bother. "
            "Now, where was I? Ah yes, haunting the living since 1423."
        )

# Example usage:
if __name__ == "__main__":
    boobot = BooBot()
    print("BooBot: Welcome to the afterlife, ghostly friend! (Type 'bye' to exit)")
    while True:
        user_input = input("You (ghost): ")
        response = boobot.respond(user_input)
        print("BooBot:", response)
        if "farewell" in response.lower():
            break