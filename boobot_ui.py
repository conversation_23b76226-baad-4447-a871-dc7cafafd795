import tkinter as tk
from tkinter import scrolledtext
from boo import BooBot
import threading
import time
import itertools
import sys

# Try to import transformers for free AI
try:
    from transformers import pipeline, set_seed
    import torch
    ai_available = True
    set_seed(42)
    ai_chatbot = pipeline('text-generation', model='microsoft/DialoGPT-small')
except Exception:
    ai_available = False
    ai_chatbot = None

try:
    import winsound
    def play_sound():
        winsound.MessageBeep()
except ImportError:
    import os
    def play_sound():
        # Simple beep for macOS/Linux
        sys.stdout.write('\a')
        sys.stdout.flush()

class BooBotUI:
    def __init__(self, root):
        self.bot = BooBot()
        self.use_ai = False
        root.title("BooBot - Afterlife Chatroom")
        root.resizable(False, False)
        root.configure(bg="#232946")

        # Header label
        header = tk.Label(root, text="BooBot - Afterlife Chatroom", font=("Courier", 18, "bold"), fg="#eebbc3", bg="#232946", pady=10)
        header.grid(row=0, column=0, columnspan=4, sticky="ew")

        # Chat area styling
        self.chat_area = scrolledtext.ScrolledText(
            root, wrap=tk.WORD, width=60, height=20, state='disabled',
            font=("Courier", 12), bg="#121629", fg="#fffffe", insertbackground="#fffffe",
            highlightthickness=0, bd=0, relief=tk.FLAT
        )
        self.chat_area.grid(row=1, column=0, columnspan=4, padx=16, pady=(0, 10))

        # Entry styling
        self.entry = tk.Entry(root, width=50, font=("Courier", 12), bg="#393d5b", fg="#fffffe", insertbackground="#fffffe", bd=0, relief=tk.FLAT)
        self.entry.grid(row=2, column=0, padx=(16, 4), pady=10, sticky="ew")
        self.entry.bind("<Return>", self.send_message)

        # Send button styling
        self.send_button = tk.Button(
            root, text="Send", command=self.send_message, font=("Courier", 12, "bold"),
            bg="#eebbc3", fg="#232946", activebackground="#b8c1ec", activeforeground="#232946",
            bd=0, relief=tk.FLAT, padx=18, pady=6, cursor="hand2"
        )
        self.send_button.grid(row=2, column=1, padx=(4, 4), pady=10, sticky="ew")

        # Clear chat button styling
        self.clear_button = tk.Button(
            root, text="Clear Chat", command=self.clear_chat, font=("Courier", 11),
            bg="#b8c1ec", fg="#232946", activebackground="#eebbc3", activeforeground="#232946",
            bd=0, relief=tk.FLAT, padx=10, pady=6, cursor="hand2"
        )
        self.clear_button.grid(row=2, column=2, padx=(4, 4), pady=10, sticky="ew")

        # AI toggle button
        self.ai_button = tk.Button(
            root, text="Enable Free AI" if ai_available else "AI Unavailable", command=self.toggle_ai, font=("Courier", 11),
            bg="#232946", fg="#eebbc3", activebackground="#eebbc3", activeforeground="#232946",
            bd=1, relief=tk.RIDGE, padx=10, pady=6, cursor="hand2", state='normal' if ai_available else 'disabled'
        )
        self.ai_button.grid(row=2, column=3, padx=(4, 16), pady=10, sticky="ew")

        root.grid_columnconfigure(0, weight=1)
        root.grid_columnconfigure(1, weight=0)
        root.grid_columnconfigure(2, weight=0)
        root.grid_columnconfigure(3, weight=0)

        self.display_message("BooBot", "Welcome to the afterlife, ghostly friend! (Type 'bye' to exit)")
        self.typing_animation_running = False

    def toggle_ai(self):
        self.use_ai = not self.use_ai
        if self.use_ai:
            self.ai_button.config(text="Disable Free AI", bg="#eebbc3", fg="#232946")
            self.display_message("System", "Free AI enabled! BooBot will now use AI responses.")
        else:
            self.ai_button.config(text="Enable Free AI", bg="#232946", fg="#eebbc3")
            self.display_message("System", "Free AI disabled. BooBot will use classic responses.")

    def display_message(self, sender, message):
        self.chat_area['state'] = 'normal'
        self.chat_area.insert(tk.END, f"{sender}: {message.strip()}\n")
        self.chat_area['state'] = 'disabled'
        self.chat_area.see(tk.END)
        play_sound()

    def clear_chat(self):
        self.chat_area['state'] = 'normal'
        self.chat_area.delete(1.0, tk.END)
        self.chat_area['state'] = 'disabled'

    def send_message(self, event=None):
        user_input = self.entry.get().strip()
        if not user_input or self.entry['state'] == 'disabled':
            return
        self.display_message("You (ghost)", user_input)
        self.entry.delete(0, tk.END)
        self.entry.config(state='disabled')
        self.send_button.config(state='disabled', bg="#b8c1ec")
        self.typing_animation_running = True
        threading.Thread(target=self.typing_animation).start()
        threading.Thread(target=self.bot_reply, args=(user_input,)).start()

    def typing_animation(self):
        dots = itertools.cycle(['.', '..', '...'])
        while self.typing_animation_running:
            self.chat_area['state'] = 'normal'
            self.chat_area.insert(tk.END, f"BooBot is materializing{next(dots)}\n")
            self.chat_area['state'] = 'disabled'
            self.chat_area.see(tk.END)
            time.sleep(0.5)
            self.chat_area['state'] = 'normal'
            self.chat_area.delete("end-2l", "end-1l")
            self.chat_area['state'] = 'disabled'

    def bot_reply(self, user_input):
        time.sleep(1.2)
        self.typing_animation_running = False
        if self.use_ai and ai_available and ai_chatbot:
            prompt = f"You: {user_input}\nBooBot:"
            try:
                ai_response = ai_chatbot(prompt, max_length=60, num_return_sequences=1)[0]['generated_text']
                print(f"[DEBUG] Raw AI response: {ai_response}")  # Debug print
                # Remove the prompt from the start, use the rest as the reply
                response = ai_response[len(prompt):]
                print(f"[DEBUG] Extracted response: {response}")  # Debug print
                if not response.strip():
                    response = "...the void is silent. Try again!"
            except Exception as e:
                response = f"[AI error: {e}]"
        else:
            response = self.bot.respond(user_input)
        self.chat_area['state'] = 'normal'
        self.chat_area.delete("end-2l", "end-1l")  # Remove typing animation
        self.chat_area['state'] = 'disabled'
        self.display_message("BooBot", response)
        if "farewell" in response.lower():
            return
        self.entry.config(state='normal')
        self.send_button.config(state='normal', bg="#eebbc3")
        self.entry.focus_set()

if __name__ == "__main__":
    root = tk.Tk()
    app = BooBotUI(root)
    root.mainloop()