# AI Salary Prediction Model using PyTorch
import torch
from torch import nn
import pandas as pd
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import numpy as np
from sklearn.preprocessing import StandardScaler
from models.model import SalaryPredictor
from utils.data_preprocessing import load_data

def train_model():
    """Train the salary prediction model"""
    print("Loading and preprocessing data...")
    X, y, x_scaler, y_scaler = load_data('data/Position_Salaries.csv')

    # Convert to tensors
    X_tensor = torch.tensor(X, dtype=torch.float32)
    y_tensor = torch.tensor(y, dtype=torch.float32)

    # Initialize model
    model = SalaryPredictor()
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.01)

    print("Training model...")
    epochs = 1000
    for epoch in range(epochs):
        model.train()
        optimizer.zero_grad()
        outputs = model(X_tensor)
        loss = criterion(outputs, y_tensor)
        loss.backward()
        optimizer.step()

        if epoch % 100 == 0:
            print(f"Epoch [{epoch}/{epochs}], Loss: {loss.item():.4f}")

    # Save model
    torch.save(model.state_dict(), 'salary_model.pth')
    print("Model saved as 'salary_model.pth'")

    return model, X_tensor, y_tensor, x_scaler, y_scaler

def evaluate_model(model, X_tensor, y_tensor, x_scaler, y_scaler):
    """Evaluate and visualize model predictions"""
    print("Evaluating model...")
    model.eval()
    with torch.no_grad():
        predictions = model(X_tensor)

    # Convert back to original scale for visualization
    X_original = x_scaler.inverse_transform(X_tensor.numpy())
    y_original = y_scaler.inverse_transform(y_tensor.numpy())
    pred_original = y_scaler.inverse_transform(predictions.numpy())

    # Plot results
    plt.figure(figsize=(10, 6))
    plt.scatter(X_original, y_original, color='blue', label='Actual Salaries')
    plt.plot(X_original, pred_original, color='red', linewidth=2, label='Predicted Salaries')
    plt.title('AI Salary Prediction Model Results')
    plt.xlabel('Position Level')
    plt.ylabel('Salary')
    plt.legend()
    plt.grid(True)
    plt.savefig('salary_prediction_results.png', dpi=300, bbox_inches='tight')
    print("Plot saved as 'salary_prediction_results.png'")

    # Calculate accuracy metrics
    mse = nn.MSELoss()(predictions, y_tensor)
    print(f"Mean Squared Error: {mse.item():.4f}")

def predict_salary(level, x_scaler, y_scaler):
    """Predict salary for a given position level"""
    # Load trained model
    model = SalaryPredictor()
    model.load_state_dict(torch.load('salary_model.pth'))
    model.eval()

    # Preprocess input
    level_scaled = x_scaler.transform([[level]])
    level_tensor = torch.tensor(level_scaled, dtype=torch.float32)

    # Make prediction
    with torch.no_grad():
        pred_scaled = model(level_tensor)
        pred_salary = y_scaler.inverse_transform(pred_scaled.numpy())

    return pred_salary[0][0]

def main():
    """Main function to run the complete pipeline"""
    print("=== AI Salary Prediction Model ===")
    print("Building AI model from scratch using PyTorch...")

    # Train the model
    model, X_tensor, y_tensor, x_scaler, y_scaler = train_model()

    # Evaluate the model
    evaluate_model(model, X_tensor, y_tensor, x_scaler, y_scaler)

    # Make some predictions
    print("\n=== Making Predictions ===")
    test_levels = [6.5, 3.5, 8.5]
    for level in test_levels:
        predicted_salary = predict_salary(level, x_scaler, y_scaler)
        print(f"Position Level {level}: Predicted Salary = ${predicted_salary:,.2f}")

if __name__ == "__main__":
    main()