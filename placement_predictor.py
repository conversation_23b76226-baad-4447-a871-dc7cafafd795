# AI-Powered Placement Eligibility Predictor
import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
import numpy as np
from datetime import datetime
import json

# Page configuration
st.set_page_config(
    page_title="AI Placement Predictor",
    page_icon="🎯",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS with proper contrast
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #2E86AB;
        text-align: center;
        margin-bottom: 2rem;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
    }
    .eligibility-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 20px;
        border-radius: 15px;
        color: white;
        text-align: center;
        margin: 20px 0;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }
    .company-card {
        background-color: #ffffff;
        padding: 15px;
        border-radius: 10px;
        border-left: 4px solid #28a745;
        margin: 10px 0;
        color: #333333;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .company-card h4 {
        color: #2E86AB;
        margin-bottom: 10px;
    }
    .company-card p {
        color: #555555;
        margin: 5px 0;
    }
    .skill-gap {
        background-color: #fff3cd;
        padding: 15px;
        border-radius: 10px;
        border-left: 4px solid #ffc107;
        margin: 10px 0;
        color: #856404;
    }
    .skill-gap h4 {
        color: #856404;
        margin-bottom: 10px;
    }
    .metric-good { color: #28a745; font-weight: bold; }
    .metric-average { color: #ffc107; font-weight: bold; }
    .metric-poor { color: #dc3545; font-weight: bold; }

    /* Fix Streamlit default styling issues */
    .stMarkdown p {
        color: #333333;
    }

    /* Ensure all text is visible */
    div[data-testid="stMarkdownContainer"] p {
        color: #333333 !important;
    }
</style>
""", unsafe_allow_html=True)

# Company database with requirements
COMPANIES_DB = {
    "Google": {
        "min_cgpa": 8.5, "min_coding_score": 85, "required_skills": ["Python", "Data Structures", "Algorithms", "System Design"],
        "preferred_skills": ["Machine Learning", "Cloud Computing"], "package": "25-45 LPA", "difficulty": "Very Hard",
        "interview_rounds": 5, "type": "Tech Giant"
    },
    "Microsoft": {
        "min_cgpa": 8.0, "min_coding_score": 80, "required_skills": ["C++", "Data Structures", "Algorithms", "OOP"],
        "preferred_skills": ["Azure", "Machine Learning"], "package": "20-40 LPA", "difficulty": "Very Hard",
        "interview_rounds": 4, "type": "Tech Giant"
    },
    "Amazon": {
        "min_cgpa": 7.5, "min_coding_score": 75, "required_skills": ["Java", "Data Structures", "Algorithms", "AWS"],
        "preferred_skills": ["System Design", "Leadership"], "package": "18-35 LPA", "difficulty": "Hard",
        "interview_rounds": 4, "type": "Tech Giant"
    },
    "Infosys": {
        "min_cgpa": 6.5, "min_coding_score": 60, "required_skills": ["Java", "SQL", "Web Development"],
        "preferred_skills": ["Spring Boot", "React"], "package": "4-8 LPA", "difficulty": "Medium",
        "interview_rounds": 3, "type": "Service Company"
    },
    "TCS": {
        "min_cgpa": 6.0, "min_coding_score": 55, "required_skills": ["Programming Basics", "SQL", "Communication"],
        "preferred_skills": ["Java", "Python"], "package": "3.5-7 LPA", "difficulty": "Easy",
        "interview_rounds": 2, "type": "Service Company"
    },
    "Wipro": {
        "min_cgpa": 6.0, "min_coding_score": 58, "required_skills": ["Java", "SQL", "Problem Solving"],
        "preferred_skills": ["Cloud", "DevOps"], "package": "3.8-7.5 LPA", "difficulty": "Easy-Medium",
        "interview_rounds": 3, "type": "Service Company"
    },
    "Accenture": {
        "min_cgpa": 6.5, "min_coding_score": 65, "required_skills": ["Programming", "Analytics", "Communication"],
        "preferred_skills": ["Consulting", "Business Analysis"], "package": "4.5-9 LPA", "difficulty": "Medium",
        "interview_rounds": 3, "type": "Consulting"
    },
    "Cognizant": {
        "min_cgpa": 6.0, "min_coding_score": 60, "required_skills": ["Java", "SQL", "Web Technologies"],
        "preferred_skills": ["Angular", "Spring"], "package": "4-8 LPA", "difficulty": "Medium",
        "interview_rounds": 3, "type": "Service Company"
    },
    "Capgemini": {
        "min_cgpa": 6.5, "min_coding_score": 62, "required_skills": ["Programming", "Database", "Testing"],
        "preferred_skills": ["Automation", "Cloud"], "package": "4.2-8.5 LPA", "difficulty": "Medium",
        "interview_rounds": 3, "type": "Service Company"
    },
    "Deloitte": {
        "min_cgpa": 7.0, "min_coding_score": 70, "required_skills": ["Analytics", "Programming", "Business Acumen"],
        "preferred_skills": ["Data Science", "Consulting"], "package": "6-12 LPA", "difficulty": "Medium-Hard",
        "interview_rounds": 4, "type": "Consulting"
    }
}

def calculate_eligibility_score(cgpa, coding_score, skills, projects, internships):
    """Calculate overall placement eligibility score"""
    # Weighted scoring
    cgpa_score = min(cgpa * 10, 100)  # CGPA out of 10 -> percentage
    coding_weight = coding_score
    skills_score = min(len(skills) * 10, 100)  # 10 points per skill, max 100
    projects_score = min(projects * 15, 60)  # 15 points per project, max 60
    internships_score = min(internships * 20, 40)  # 20 points per internship, max 40
    
    total_score = (cgpa_score * 0.25 + coding_weight * 0.30 + skills_score * 0.20 + 
                   projects_score * 0.15 + internships_score * 0.10)
    
    return min(total_score, 100)

def get_eligible_companies(cgpa, coding_score, skills):
    """Get list of companies user is eligible for"""
    eligible = []
    for company, requirements in COMPANIES_DB.items():
        if cgpa >= requirements["min_cgpa"] and coding_score >= requirements["min_coding_score"]:
            skill_match = len(set(skills) & set(requirements["required_skills"]))
            if skill_match >= len(requirements["required_skills"]) * 0.6:  # 60% skill match
                eligible.append({
                    "company": company,
                    "skill_match": skill_match,
                    "total_required": len(requirements["required_skills"]),
                    "details": requirements
                })
    return sorted(eligible, key=lambda x: x["skill_match"], reverse=True)

def get_skill_gaps(user_skills, target_companies):
    """Identify missing skills for target companies"""
    all_required = set()
    all_preferred = set()
    
    for company in target_companies:
        company_data = COMPANIES_DB[company]
        all_required.update(company_data["required_skills"])
        all_preferred.update(company_data["preferred_skills"])
    
    missing_required = all_required - set(user_skills)
    missing_preferred = all_preferred - set(user_skills)
    
    return list(missing_required), list(missing_preferred)

def create_eligibility_radar(cgpa, coding_score, skills_count, projects, internships):
    """Create radar chart for profile assessment"""
    categories = ['CGPA', 'Coding Skills', 'Technical Skills', 'Projects', 'Experience']
    
    # Normalize scores to 0-100 scale
    scores = [
        min(cgpa * 10, 100),
        coding_score,
        min(skills_count * 10, 100),
        min(projects * 20, 100),
        min(internships * 25, 100)
    ]
    
    fig = go.Figure()
    
    fig.add_trace(go.Scatterpolar(
        r=scores,
        theta=categories,
        fill='toself',
        name='Your Profile',
        line_color='rgb(46, 134, 171)'
    ))
    
    fig.update_layout(
        polar=dict(
            radialaxis=dict(
                visible=True,
                range=[0, 100]
            )),
        showlegend=True,
        title="Profile Assessment Radar"
    )
    
    return fig

def predict_placement_success(cgpa, coding_score, skills_count, projects, internships):
    """ML-based placement success prediction"""
    # Simple ML model simulation (in real app, you'd use trained model)
    features = np.array([cgpa, coding_score, skills_count, projects, internships])

    # Weighted prediction based on industry standards
    weights = np.array([0.25, 0.30, 0.20, 0.15, 0.10])
    normalized_features = np.array([
        min(cgpa * 10, 100),
        coding_score,
        min(skills_count * 10, 100),
        min(projects * 20, 100),
        min(internships * 25, 100)
    ])

    success_probability = np.dot(normalized_features, weights) / 100
    return min(success_probability, 1.0)

def get_learning_path(missing_skills, target_companies):
    """Generate personalized learning path"""
    skill_priority = {
        "Data Structures": {"time": "2-3 months", "difficulty": "Medium", "resources": ["LeetCode", "GeeksforGeeks"]},
        "Algorithms": {"time": "2-3 months", "difficulty": "Medium", "resources": ["Coursera", "YouTube"]},
        "Python": {"time": "1-2 months", "difficulty": "Easy", "resources": ["Codecademy", "Python.org"]},
        "Java": {"time": "1-2 months", "difficulty": "Easy", "resources": ["Oracle Java Tutorials", "Coursera"]},
        "Machine Learning": {"time": "3-4 months", "difficulty": "Hard", "resources": ["Coursera ML Course", "Kaggle"]},
        "System Design": {"time": "2-3 months", "difficulty": "Hard", "resources": ["System Design Primer", "YouTube"]},
        "Web Development": {"time": "2-3 months", "difficulty": "Medium", "resources": ["FreeCodeCamp", "MDN Docs"]},
        "SQL": {"time": "3-4 weeks", "difficulty": "Easy", "resources": ["W3Schools", "SQLBolt"]},
        "AWS": {"time": "2-3 months", "difficulty": "Medium", "resources": ["AWS Training", "A Cloud Guru"]},
        "React": {"time": "1-2 months", "difficulty": "Medium", "resources": ["React Docs", "Scrimba"]},
    }

    learning_plan = []
    for skill in missing_skills[:5]:  # Top 5 priority skills
        if skill in skill_priority:
            learning_plan.append({
                "skill": skill,
                "details": skill_priority[skill]
            })

    return learning_plan

def main():
    # Header
    st.markdown('<h1 class="main-header">🎯 AI Placement Eligibility Predictor</h1>', unsafe_allow_html=True)
    st.markdown("### Discover your placement readiness, eligible companies, and skill gaps!")

    # Sidebar for user input
    st.sidebar.header("📝 Your Profile")
    
    # Academic details
    st.sidebar.subheader("🎓 Academic Performance")
    cgpa = st.sidebar.slider("CGPA", 0.0, 10.0, 7.5, 0.1)
    
    # Coding skills
    st.sidebar.subheader("💻 Coding Assessment")
    coding_score = st.sidebar.slider("Coding Score (%)", 0, 100, 70, 5)
    
    # Technical skills
    st.sidebar.subheader("🛠️ Technical Skills")
    all_skills = ["Python", "Java", "C++", "JavaScript", "Data Structures", "Algorithms", 
                  "SQL", "Web Development", "Machine Learning", "System Design", "AWS", 
                  "React", "Angular", "Spring Boot", "DevOps", "Cloud Computing", "OOP",
                  "Problem Solving", "Communication", "Leadership", "Analytics"]
    
    selected_skills = st.sidebar.multiselect("Select your skills:", all_skills)
    
    # Experience
    st.sidebar.subheader("📈 Experience")
    projects = st.sidebar.number_input("Number of Projects", 0, 20, 3)
    internships = st.sidebar.number_input("Number of Internships", 0, 10, 1)
    
    # Target companies
    st.sidebar.subheader("🎯 Target Companies")
    target_companies = st.sidebar.multiselect(
        "Companies you're interested in:",
        list(COMPANIES_DB.keys()),
        default=["Google", "Microsoft", "TCS", "Infosys"]
    )
    
    # Calculate eligibility and predictions
    eligibility_score = calculate_eligibility_score(cgpa, coding_score, selected_skills, projects, internships)
    eligible_companies = get_eligible_companies(cgpa, coding_score, selected_skills)
    placement_probability = predict_placement_success(cgpa, coding_score, len(selected_skills), projects, internships)
    
    # Main content
    col1, col2 = st.columns([2, 1])
    
    with col1:
        # Eligibility Score
        st.markdown(f"""
        <div class="eligibility-card">
            <h2>🎯 Placement Eligibility Score</h2>
            <h1 style="font-size: 4rem; margin: 20px 0;">{eligibility_score:.1f}/100</h1>
            <p style="font-size: 1.2rem;">
                {"🟢 Excellent" if eligibility_score >= 80 else 
                 "🟡 Good" if eligibility_score >= 60 else 
                 "🟠 Average" if eligibility_score >= 40 else "🔴 Needs Improvement"}
            </p>
        </div>
        """, unsafe_allow_html=True)
        
        # Eligible Companies
        st.subheader("✅ Companies You're Eligible For")
        if eligible_companies:
            for company_info in eligible_companies:
                company = company_info["company"]
                details = company_info["details"]
                skill_match = company_info["skill_match"]
                total_required = company_info["total_required"]
                
                st.markdown(f"""
                <div class="company-card">
                    <h4>{company} ({details['type']})</h4>
                    <p><strong>Package:</strong> {details['package']} | <strong>Difficulty:</strong> {details['difficulty']}</p>
                    <p><strong>Skill Match:</strong> {skill_match}/{total_required} required skills</p>
                    <p><strong>Interview Rounds:</strong> {details['interview_rounds']}</p>
                </div>
                """, unsafe_allow_html=True)
        else:
            st.warning("⚠️ You don't meet the minimum requirements for any companies yet. Focus on improving your profile!")
    
    with col2:
        # Profile radar chart
        st.subheader("📊 Profile Assessment")
        radar_fig = create_eligibility_radar(cgpa, coding_score, len(selected_skills), projects, internships)
        st.plotly_chart(radar_fig, use_container_width=True)
        
        # Quick stats
        st.subheader("📈 Quick Stats")
        st.metric("Eligible Companies", len(eligible_companies))
        st.metric("Skills Count", len(selected_skills))
        st.metric("Profile Strength", f"{eligibility_score:.0f}%")
    
    # Skill Gap Analysis
    if target_companies:
        st.subheader("🎯 Skill Gap Analysis for Target Companies")
        missing_required, missing_preferred = get_skill_gaps(selected_skills, target_companies)
        
        col3, col4 = st.columns(2)
        
        with col3:
            if missing_required:
                st.markdown("""
                <div class="skill-gap">
                    <h4>🚨 Critical Skills Missing</h4>
                    <p>These skills are <strong>required</strong> for your target companies:</p>
                </div>
                """, unsafe_allow_html=True)
                for skill in missing_required:
                    st.error(f"❌ {skill}")
            else:
                st.success("✅ You have all required skills for your targets!")
        
        with col4:
            if missing_preferred:
                st.markdown("""
                <div class="skill-gap">
                    <h4>⭐ Preferred Skills to Learn</h4>
                    <p>These skills will give you an <strong>advantage</strong>:</p>
                </div>
                """, unsafe_allow_html=True)
                for skill in missing_preferred:
                    st.warning(f"⚡ {skill}")
            else:
                st.success("🌟 You have excellent skill coverage!")
    
    # Recommendations
    st.subheader("💡 Personalized Recommendations")
    
    recommendations = []
    if cgpa < 7.0:
        recommendations.append("📚 Focus on improving your CGPA - many companies have minimum requirements")
    if coding_score < 70:
        recommendations.append("💻 Practice coding on platforms like LeetCode, HackerRank, or CodeChef")
    if len(selected_skills) < 5:
        recommendations.append("🛠️ Learn more technical skills relevant to your field")
    if projects < 3:
        recommendations.append("🚀 Build more projects to showcase your practical skills")
    if internships == 0:
        recommendations.append("🏢 Gain internship experience to boost your profile")
    
    if recommendations:
        for rec in recommendations:
            st.info(rec)
    else:
        st.success("🎉 Your profile looks strong! Keep up the excellent work!")
    
    # Company comparison table
    if len(eligible_companies) > 1:
        st.subheader("📊 Company Comparison")
        comparison_data = []
        for company_info in eligible_companies:
            company = company_info["company"]
            details = company_info["details"]
            comparison_data.append({
                "Company": company,
                "Package": details["package"],
                "Difficulty": details["difficulty"],
                "Type": details["type"],
                "Min CGPA": details["min_cgpa"],
                "Min Coding": details["min_coding_score"]
            })
        
        df = pd.DataFrame(comparison_data)
        st.dataframe(df, use_container_width=True)

if __name__ == "__main__":
    main()
