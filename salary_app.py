# Interactive AI Salary Prediction Web App
import streamlit as st
import torch
from torch import nn
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
import numpy as np
from models.model import SalaryPredictor
from utils.data_preprocessing import load_data
import os

# Page configuration
st.set_page_config(
    page_title="AI Salary Predictor",
    page_icon="💰",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .prediction-box {
        background-color: #f0f8ff;
        padding: 20px;
        border-radius: 10px;
        border-left: 5px solid #1f77b4;
        margin: 20px 0;
    }
    .metric-card {
        background-color: #ffffff;
        padding: 15px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        text-align: center;
    }
</style>
""", unsafe_allow_html=True)

def load_trained_model():
    """Load the pre-trained model and scalers"""
    try:
        # Load data to get scalers
        X, y, x_scaler, y_scaler = load_data('data/Position_Salaries.csv')
        
        # Load model
        model = SalaryPredictor()
        if os.path.exists('salary_model.pth'):
            model.load_state_dict(torch.load('salary_model.pth'))
            model.eval()
            return model, x_scaler, y_scaler, X, y
        else:
            return None, None, None, None, None
    except Exception as e:
        st.error(f"Error loading model: {e}")
        return None, None, None, None, None

def predict_salary(model, x_scaler, y_scaler, position_level):
    """Make salary prediction for given position level"""
    try:
        # Preprocess input
        level_scaled = x_scaler.transform([[position_level]])
        level_tensor = torch.tensor(level_scaled, dtype=torch.float32)
        
        # Make prediction
        with torch.no_grad():
            pred_scaled = model(level_tensor)
            pred_salary = y_scaler.inverse_transform(pred_scaled.numpy())
        
        return pred_salary[0][0]
    except Exception as e:
        st.error(f"Error making prediction: {e}")
        return None

def create_visualization(X, y, x_scaler, y_scaler, model, user_prediction=None):
    """Create interactive visualization"""
    # Convert back to original scale
    X_original = x_scaler.inverse_transform(X)
    y_original = y_scaler.inverse_transform(y)
    
    # Generate smooth curve for predictions
    x_range = np.linspace(1, 10, 100).reshape(-1, 1)
    x_range_scaled = x_scaler.transform(x_range)
    x_range_tensor = torch.tensor(x_range_scaled, dtype=torch.float32)
    
    with torch.no_grad():
        y_pred_scaled = model(x_range_tensor)
        y_pred = y_scaler.inverse_transform(y_pred_scaled.numpy())
    
    # Create plotly figure
    fig = go.Figure()
    
    # Add actual data points
    fig.add_trace(go.Scatter(
        x=X_original.flatten(),
        y=y_original.flatten(),
        mode='markers',
        name='Actual Salaries',
        marker=dict(size=12, color='blue', symbol='circle'),
        hovertemplate='Position Level: %{x}<br>Salary: $%{y:,.0f}<extra></extra>'
    ))
    
    # Add prediction curve
    fig.add_trace(go.Scatter(
        x=x_range.flatten(),
        y=y_pred.flatten(),
        mode='lines',
        name='AI Predictions',
        line=dict(color='red', width=3),
        hovertemplate='Position Level: %{x:.1f}<br>Predicted Salary: $%{y:,.0f}<extra></extra>'
    ))
    
    # Add user prediction if provided
    if user_prediction:
        level, salary = user_prediction
        fig.add_trace(go.Scatter(
            x=[level],
            y=[salary],
            mode='markers',
            name='Your Prediction',
            marker=dict(size=15, color='green', symbol='star'),
            hovertemplate=f'Your Input<br>Position Level: {level}<br>Predicted Salary: ${salary:,.0f}<extra></extra>'
        ))
    
    # Update layout
    fig.update_layout(
        title='AI Salary Prediction Model Results',
        xaxis_title='Position Level',
        yaxis_title='Salary ($)',
        hovermode='closest',
        height=500,
        showlegend=True
    )
    
    return fig

def main():
    # Header
    st.markdown('<h1 class="main-header">💰 AI Salary Predictor</h1>', unsafe_allow_html=True)
    st.markdown("### Predict employee salaries using advanced AI neural networks")
    
    # Load model
    model, x_scaler, y_scaler, X, y = load_trained_model()
    
    if model is None:
        st.error("❌ Model not found! Please run the training script first.")
        st.info("Run `python new.py` to train the model first.")
        return
    
    # Sidebar for inputs
    st.sidebar.header("🎯 Make Predictions")
    st.sidebar.markdown("Enter a position level to predict the salary:")
    
    # Position level input
    position_level = st.sidebar.slider(
        "Position Level",
        min_value=1.0,
        max_value=10.0,
        value=5.0,
        step=0.1,
        help="1 = Entry Level, 10 = CEO Level"
    )
    
    # Alternative input methods
    st.sidebar.markdown("---")
    st.sidebar.markdown("**Or enter exact value:**")
    custom_level = st.sidebar.number_input(
        "Custom Position Level",
        min_value=0.1,
        max_value=15.0,
        value=position_level,
        step=0.1
    )
    
    # Use custom level if different from slider
    if custom_level != position_level:
        position_level = custom_level
    
    # Predict button
    if st.sidebar.button("🚀 Predict Salary", type="primary"):
        prediction = predict_salary(model, x_scaler, y_scaler, position_level)
        
        if prediction is not None:
            st.session_state.prediction = prediction
            st.session_state.position_level = position_level
    
    # Main content area
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.subheader("📊 Visualization")
        
        # Show prediction if available
        user_pred = None
        if hasattr(st.session_state, 'prediction'):
            user_pred = (st.session_state.position_level, st.session_state.prediction)
        
        # Create and display chart
        fig = create_visualization(X, y, x_scaler, y_scaler, model, user_pred)
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        st.subheader("💡 Prediction Results")
        
        if hasattr(st.session_state, 'prediction'):
            st.markdown(f"""
            <div class="prediction-box">
                <h3>🎯 Prediction for Level {st.session_state.position_level}</h3>
                <h2 style="color: #1f77b4; font-size: 2.5rem;">${st.session_state.prediction:,.0f}</h2>
                <p><strong>Annual Salary Prediction</strong></p>
            </div>
            """, unsafe_allow_html=True)
            
            # Position interpretation
            level = st.session_state.position_level
            if level <= 2:
                role = "Entry Level / Junior"
            elif level <= 4:
                role = "Mid-Level / Manager"
            elif level <= 6:
                role = "Senior / Regional Manager"
            elif level <= 8:
                role = "Executive / Partner"
            else:
                role = "C-Level / CEO"
            
            st.info(f"**Position Category:** {role}")
        else:
            st.info("👈 Use the sidebar to make a prediction!")
        
        # Model info
        st.markdown("---")
        st.subheader("🤖 Model Information")
        st.markdown("""
        - **Architecture:** Neural Network (3 layers)
        - **Training:** 1000 epochs
        - **Accuracy:** Very High (MSE ≈ 0)
        - **Framework:** PyTorch
        """)
    
    # Additional features
    st.markdown("---")
    st.subheader("📈 Batch Predictions")
    
    col3, col4 = st.columns(2)
    
    with col3:
        st.markdown("**Quick Predictions:**")
        quick_levels = [2.5, 5.0, 7.5]
        for level in quick_levels:
            pred = predict_salary(model, x_scaler, y_scaler, level)
            if pred:
                st.metric(f"Level {level}", f"${pred:,.0f}")
    
    with col4:
        st.markdown("**Position Reference:**")
        st.markdown("""
        - **1-2:** Business Analyst, Junior Consultant
        - **3-4:** Senior Consultant, Manager
        - **5-6:** Country Manager, Region Manager
        - **7-8:** Partner, Senior Partner
        - **9-10:** C-level, CEO
        """)

if __name__ == "__main__":
    main()
