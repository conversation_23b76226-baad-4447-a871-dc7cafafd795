# Simple AI Placement Eligibility Predictor with Visible Text
import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import numpy as np

# Page configuration
st.set_page_config(
    page_title="AI Placement Predictor",
    page_icon="🎯",
    layout="wide"
)

# Company database
COMPANIES_DB = {
    "Google": {"min_cgpa": 8.5, "min_coding": 85, "skills": ["Python", "Data Structures", "Algorithms"], "package": "25-45 LPA"},
    "Microsoft": {"min_cgpa": 8.0, "min_coding": 80, "skills": ["C++", "Data Structures", "OOP"], "package": "20-40 LPA"},
    "Amazon": {"min_cgpa": 7.5, "min_coding": 75, "skills": ["Java", "Data Structures", "AWS"], "package": "18-35 LPA"},
    "Infosys": {"min_cgpa": 6.5, "min_coding": 60, "skills": ["Java", "SQL", "Web Development"], "package": "4-8 LPA"},
    "TCS": {"min_cgpa": 6.0, "min_coding": 55, "skills": ["Programming", "SQL"], "package": "3.5-7 LPA"},
    "Wipro": {"min_cgpa": 6.0, "min_coding": 58, "skills": ["Java", "SQL"], "package": "3.8-7.5 LPA"},
    "Accenture": {"min_cgpa": 6.5, "min_coding": 65, "skills": ["Programming", "Analytics"], "package": "4.5-9 LPA"},
    "Cognizant": {"min_cgpa": 6.0, "min_coding": 60, "skills": ["Java", "SQL"], "package": "4-8 LPA"},
    "Deloitte": {"min_cgpa": 7.0, "min_coding": 70, "skills": ["Analytics", "Programming"], "package": "6-12 LPA"}
}

def calculate_score(cgpa, coding, skills_count, projects, internships):
    """Calculate placement readiness score"""
    cgpa_score = min(cgpa * 10, 100)
    skills_score = min(skills_count * 8, 80)
    projects_score = min(projects * 15, 60)
    internships_score = min(internships * 20, 40)
    
    total = (cgpa_score * 0.3 + coding * 0.3 + skills_score * 0.2 + 
             projects_score * 0.1 + internships_score * 0.1)
    return min(total, 100)

def get_eligible_companies(cgpa, coding, user_skills):
    """Get eligible companies"""
    eligible = []
    for company, req in COMPANIES_DB.items():
        if cgpa >= req["min_cgpa"] and coding >= req["min_coding"]:
            skill_match = len(set(user_skills) & set(req["skills"]))
            if skill_match >= len(req["skills"]) * 0.5:  # 50% skill match
                eligible.append({
                    "name": company,
                    "package": req["package"],
                    "skill_match": f"{skill_match}/{len(req['skills'])}"
                })
    return eligible

def main():
    # Title
    st.title("🎯 AI Placement Eligibility Predictor")
    st.markdown("**Discover your placement readiness and eligible companies!**")
    
    # Create two columns
    col1, col2 = st.columns([1, 2])
    
    with col1:
        st.header("📝 Your Profile")
        
        # Input fields
        cgpa = st.slider("CGPA", 0.0, 10.0, 7.5, 0.1)
        coding_score = st.slider("Coding Score (%)", 0, 100, 70)
        
        # Skills selection
        all_skills = ["Python", "Java", "C++", "JavaScript", "Data Structures", 
                     "Algorithms", "SQL", "Web Development", "Machine Learning", 
                     "AWS", "React", "Angular", "OOP", "Analytics"]
        
        selected_skills = st.multiselect("Select your skills:", all_skills)
        
        projects = st.number_input("Number of Projects", 0, 20, 3)
        internships = st.number_input("Number of Internships", 0, 10, 1)
        
        # Calculate button
        if st.button("🚀 Analyze My Profile", type="primary"):
            st.session_state.analyze = True
    
    with col2:
        if hasattr(st.session_state, 'analyze') and st.session_state.analyze:
            # Calculate scores
            score = calculate_score(cgpa, coding_score, len(selected_skills), projects, internships)
            eligible = get_eligible_companies(cgpa, coding_score, selected_skills)
            
            # Display results
            st.header("📊 Your Results")
            
            # Score display
            st.subheader("🎯 Placement Readiness Score")
            
            # Create a nice score display
            if score >= 80:
                st.success(f"**{score:.1f}/100** - Excellent! You're ready for top companies!")
            elif score >= 60:
                st.warning(f"**{score:.1f}/100** - Good! Focus on improving weak areas.")
            else:
                st.error(f"**{score:.1f}/100** - Needs improvement. Work on fundamentals.")
            
            # Progress bar
            st.progress(score/100)
            
            # Eligible companies
            st.subheader("✅ Companies You're Eligible For")
            if eligible:
                for company in eligible:
                    st.info(f"**{company['name']}** - {company['package']} | Skills: {company['skill_match']}")
            else:
                st.warning("No companies match your current profile. Keep improving!")
            
            # Recommendations
            st.subheader("💡 Recommendations")
            recommendations = []
            
            if cgpa < 7.0:
                recommendations.append("📚 Improve your CGPA - aim for 7.0+")
            if coding_score < 70:
                recommendations.append("💻 Practice coding - aim for 70%+")
            if len(selected_skills) < 5:
                recommendations.append("🛠️ Learn more technical skills")
            if projects < 3:
                recommendations.append("🚀 Build more projects")
            if internships == 0:
                recommendations.append("🏢 Get internship experience")
            
            if recommendations:
                for rec in recommendations:
                    st.write(f"• {rec}")
            else:
                st.success("🎉 Great profile! You're on the right track!")
            
            # Skills gap analysis
            if len(selected_skills) > 0:
                st.subheader("🎯 Skill Gap Analysis")
                
                # Find missing skills for top companies
                top_company_skills = set()
                for company in ["Google", "Microsoft", "Amazon"]:
                    top_company_skills.update(COMPANIES_DB[company]["skills"])
                
                missing_skills = top_company_skills - set(selected_skills)
                
                if missing_skills:
                    st.write("**Skills to learn for top companies:**")
                    for skill in list(missing_skills)[:5]:  # Show top 5
                        st.write(f"• {skill}")
                else:
                    st.success("You have all key skills for top companies!")
    
    # Company comparison table
    st.header("🏢 Company Requirements Comparison")
    
    # Create comparison dataframe
    comparison_data = []
    for company, req in COMPANIES_DB.items():
        comparison_data.append({
            "Company": company,
            "Min CGPA": req["min_cgpa"],
            "Min Coding Score": f"{req['min_coding']}%",
            "Key Skills": ", ".join(req["skills"][:3]),  # Show first 3 skills
            "Package": req["package"]
        })
    
    df = pd.DataFrame(comparison_data)
    st.dataframe(df, use_container_width=True)
    
    # Tips section
    st.header("💡 Placement Preparation Tips")
    
    tips_col1, tips_col2 = st.columns(2)
    
    with tips_col1:
        st.subheader("📚 Academic Excellence")
        st.write("• Maintain CGPA above 7.0")
        st.write("• Focus on core subjects")
        st.write("• Participate in academic projects")
        
        st.subheader("💻 Technical Skills")
        st.write("• Master Data Structures & Algorithms")
        st.write("• Learn popular programming languages")
        st.write("• Practice on coding platforms")
    
    with tips_col2:
        st.subheader("🚀 Practical Experience")
        st.write("• Build real-world projects")
        st.write("• Contribute to open source")
        st.write("• Get internship experience")
        
        st.subheader("🎯 Interview Preparation")
        st.write("• Practice mock interviews")
        st.write("• Improve communication skills")
        st.write("• Stay updated with industry trends")

if __name__ == "__main__":
    main()
