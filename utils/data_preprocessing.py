# utils/data_preprocessing.py
import pandas as pd
from sklearn.preprocessing import StandardScaler

def load_data(path):
    """Load and preprocess the salary data"""
    df = pd.read_csv(path)
    X = df[['Level']].values
    y = df['Salary'].values.reshape(-1, 1)

    x_scaler = StandardScaler()
    y_scaler = StandardScaler()

    X_scaled = x_scaler.fit_transform(X)
    y_scaled = y_scaler.fit_transform(y)

    return X_scaled, y_scaled, x_scaler, y_scaler
